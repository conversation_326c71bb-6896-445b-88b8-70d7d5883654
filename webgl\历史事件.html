<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>历史事件</title>
    <link rel="shortcut icon" href="logo.png">
    <link rel="stylesheet" href="styles.css">
    <!-- 引入 echarts -->
    <!-- <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script> -->
    <script src="./echarts/echarts.min.js"></script>
    <!-- 引入字体图标 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- 引入配置文件 -->
    <script src="config.js"></script>
    <style>
        /**
         * 历史事件页面专用样式
         * 基于项目统一的科技蓝色主题设计
         */
        .history-events-container {
            width: 100vw;
            height: 100vh;
            margin: 0;
            background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
            color: var(--text-primary);
            overflow: hidden;
            position: relative;
            font-family: var(--font-family);
            display: flex;
            flex-direction: column;
        }

        /* 科技感背景动画 */
        .history-events-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 20%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(0, 153, 204, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 60%, rgba(102, 224, 255, 0.05) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
        }

        /* 顶部标题栏 */
        .history-header {
            height: 100px;
            background: linear-gradient(90deg,
                rgba(26, 31, 46, 0.95) 0%,
                rgba(42, 49, 66, 0.95) 50%,
                rgba(26, 31, 46, 0.95) 100%);
            backdrop-filter: blur(10px);
            border-bottom: 2px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 40px;
            box-shadow: var(--shadow-secondary);
            position: relative;
            z-index: 100;
        }

        .history-header::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 2px;
            background: linear-gradient(90deg,
                transparent 0%,
                var(--primary-color) 25%,
                var(--accent-color) 50%,
                var(--primary-color) 75%,
                transparent 100%);
            background-size: 200% 100%;
            animation: gradientShift 3s ease-in-out infinite;
        }

        .history-title {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .history-title h1 {
            font-size: 36px;
            font-weight: bold;
            background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin: 0;
        }

        .history-title i {
            font-size: 42px;
            color: var(--primary-color);
        }



        /* 主内容区域 */
        .history-main {
            flex: 1;
            display: grid;
            grid-template-columns: 350px 1fr;
            gap: 30px;
            padding: 30px;
            min-height: 0;
            overflow: hidden;
        }

        /* 左侧筛选面板 */
        .filter-panel {
            background: rgba(26, 31, 46, 0.8);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            backdrop-filter: blur(10px);
            box-shadow: var(--shadow-primary);
            overflow: hidden;
            display: flex;
            flex-direction: column;
            min-height: 0;
        }

        .filter-header {
            background: linear-gradient(135deg,
                rgba(0, 212, 255, 0.2) 0%,
                rgba(0, 153, 204, 0.2) 100%);
            padding: 15px 20px;
            border-bottom: 1px solid var(--border-color);
        }

        .filter-header h3 {
            margin: 0;
            font-size: 18px;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .filter-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            min-height: 200px;
        }

        .filter-section {
            margin-bottom: 25px;
        }

        .filter-section h4 {
            font-size: 14px;
            color: var(--primary-color);
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 600;
            background: linear-gradient(90deg, rgba(0, 212, 255, 0.12), rgba(0, 153, 204, 0.08), transparent);
            padding: 6px 10px;
            border-radius: 6px;
            border-left: 3px solid var(--primary-color);
        }

        .filter-buttons {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .filter-btn {
            background: rgba(0, 212, 255, 0.05);
            border: 1px solid rgba(0, 212, 255, 0.2);
            color: var(--text-secondary);
            padding: 10px 15px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 13px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .filter-btn:hover {
            background: rgba(0, 212, 255, 0.1);
            border-color: var(--primary-color);
            color: var(--primary-color);
            transform: translateX(5px);
        }

        .filter-btn.active {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border-color: var(--primary-color);
            color: var(--bg-primary);
            font-weight: 600;
        }

        .date-range-inputs {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .quick-date-buttons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
            margin: 10px 0;
        }

        .quick-date-buttons .filter-btn {
            padding: 8px 12px;
            font-size: 12px;
            text-align: center;
            justify-content: center;
        }

        .date-input-group {
            position: relative;
            display: flex;
            align-items: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .date-input-group:hover {
            transform: translateY(-1px);
        }

        .date-input-group:hover .date-input {
            border-color: var(--primary-color);
            box-shadow: 0 4px 12px rgba(0, 212, 255, 0.3);
            background: rgba(0, 212, 255, 0.12);
        }

        .date-input-group:active {
            transform: translateY(0);
        }

        .date-input-group:active .date-input {
            box-shadow: 0 2px 8px rgba(0, 212, 255, 0.4);
        }

        .date-input {
            background: rgba(0, 212, 255, 0.08);
            border: 1px solid rgba(0, 212, 255, 0.3);
            color: var(--text-primary);
            padding: 12px 40px 12px 15px;
            border-radius: 8px;
            font-size: 14px;
            font-family: var(--font-mono);
            width: 100%;
            transition: all 0.3s ease;
            /* 强制隐藏原生datetime-local图标 */
            appearance: none !important;
            -webkit-appearance: none !important;
            -moz-appearance: none !important;
        }

        /* 针对WebKit浏览器强制隐藏日历图标 */
        .date-input::-webkit-calendar-picker-indicator {
            opacity: 0;
            cursor: pointer;
            width: 0;
            height: 0;
            display: none;
        }

        /* 针对Firefox的额外处理 */
        .date-input::-moz-calendar-picker-indicator {
            opacity: 0;
            cursor: pointer;
            width: 0;
            height: 0;
            display: none;
        }

        .date-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 15px rgba(0, 212, 255, 0.4);
            background: rgba(0, 212, 255, 0.12);
        }

        .date-input::placeholder {
            color: rgba(255, 255, 255, 0.5);
            font-size: 13px;
        }

        .date-input-icon {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--primary-color);
            font-size: 16px;
            pointer-events: none;
            transition: all 0.3s ease;
            z-index: 1;
        }

        .date-input:focus + .date-input-icon {
            color: var(--accent-color);
            transform: translateY(-50%) scale(1.1);
        }

        .date-input-group:hover .date-input-icon {
            color: var(--accent-color);
            animation: pulse 1.5s ease-in-out infinite;
        }

        /* 右侧事件列表 */
        .events-panel {
            background: rgba(26, 31, 46, 0.8);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            backdrop-filter: blur(10px);
            box-shadow: var(--shadow-primary);
            overflow: hidden;
            display: flex;
            flex-direction: column;
            min-height: 0;
        }

        .events-header {
            background: linear-gradient(135deg,
                rgba(0, 212, 255, 0.2) 0%,
                rgba(0, 153, 204, 0.2) 100%);
            padding: 15px 20px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .events-header h3 {
            margin: 0;
            font-size: 22px;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .events-stats {
            display: flex;
            gap: 20px;
            font-size: 14px;
            color: var(--text-secondary);
        }

        .stat-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .stat-count {
            font-weight: bold;
            color: var(--primary-color);
            font-family: var(--font-mono);
        }

        /* 事件列表表头 */
        .events-table-header {
            display: grid;
            grid-template-columns: 60px 220px 1fr;
            gap: 10px;
            padding: 16px 30px;
            background: rgba(0, 212, 255, 0.1);
            border-bottom: 1px solid var(--border-color);
            font-size: 15px;
            font-weight: 600;
            color: var(--primary-color);
        }

        .header-cell {
            display: flex;
            align-items: center;
            gap: 8px;
            white-space: nowrap;
        }

        .header-cell.serial {
            justify-content: flex-end;
            padding-right: 10px;
            white-space: nowrap;
        }

        /* 事件列表内容 */
        .events-content {
            flex: 1;
            overflow-y: auto;
            padding: 0;
            min-height: 200px;
        }

        .event-item {
            display: grid;
            grid-template-columns: 60px 220px 1fr;
            gap: 10px;
            padding: 15px 30px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.05);
            align-items: center;
            font-size: 14px;
            transition: all 0.3s ease;
            cursor: pointer;
            min-height: 60px;
        }

        .event-item:hover {
            background: rgba(0, 212, 255, 0.08);
            border-left: 4px solid var(--primary-color);
            padding-left: 26px;
        }

        .event-serial {
            text-align: right;
            font-weight: bold;
            font-family: var(--font-mono);
            padding-right: 10px;
            border-right: 1px solid rgba(255, 255, 255, 0.1);
            font-size: 15px;
        }

        .event-datetime {
            font-family: var(--font-mono);
            font-size: 13px;
        }

        .event-message {
            color: var(--text-secondary);
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            line-height: 1.5;
        }

        /* 不同类型事件的颜色 */
        .event-item.alarm .event-serial,
        .event-item.alarm .event-datetime {
            color: var(--warning-color);
        }

        .event-item.fault .event-serial,
        .event-item.fault .event-datetime {
            color: var(--error-color);
        }

        /* 空状态显示 */
        .empty-state {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 300px;
            color: var(--text-muted);
        }

        .empty-state i {
            font-size: 48px;
            color: var(--primary-color);
            margin-bottom: 15px;
            opacity: 0.5;
        }

        .empty-state p {
            font-size: 16px;
            margin: 0;
        }

        /* 滚动条样式优化 */
        .filter-content::-webkit-scrollbar,
        .events-content::-webkit-scrollbar {
            width: 6px;
        }

        .filter-content::-webkit-scrollbar-track,
        .events-content::-webkit-scrollbar-track {
            background: rgba(0, 212, 255, 0.1);
            border-radius: 3px;
        }

        .filter-content::-webkit-scrollbar-thumb,
        .events-content::-webkit-scrollbar-thumb {
            background: linear-gradient(180deg, var(--primary-color), var(--secondary-color));
            border-radius: 3px;
        }

        .filter-content::-webkit-scrollbar-thumb:hover,
        .events-content::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(180deg, var(--accent-color), var(--primary-color));
        }

        /* 分页控件样式 */
        #pagination-controls {
            padding: 15px 20px;
            border-top: 1px solid var(--border-color);
            background: rgba(0, 212, 255, 0.05);
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
            margin-bottom: 0;
        }

        #pagination-controls .filter-btn {
            padding: 6px 12px;
            font-size: 11px;
            min-width: auto;
        }

        #pagination-controls .filter-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            pointer-events: none;
        }

        /* 脉冲动画 */
        @keyframes pulse {
            0% {
                opacity: 1;
                transform: translateY(-50%) scale(1);
            }
            50% {
                opacity: 0.7;
                transform: translateY(-50%) scale(1.1);
            }
            100% {
                opacity: 1;
                transform: translateY(-50%) scale(1);
            }
        }

        /* 响应式优化 */
        @media (max-width: 1920px) {
            .history-events-container {
                width: 100%;
                max-width: 1920px;
                height: 100vh;
                max-height: 1080px;
            }
        }

        @media (max-width: 1600px) {
            .history-main {
                grid-template-columns: 320px 1fr;
                gap: 25px;
                padding: 25px;
            }
        }

        @media (max-width: 1400px) {
            .history-events-container {
                width: 100%;
                max-width: 1366px;
            }

            .history-main {
                grid-template-columns: 280px 1fr;
                gap: 15px;
                padding: 15px;
            }
        }

        @media (max-width: 1200px) {
            .history-main {
                grid-template-columns: 1fr;
                grid-template-rows: auto 1fr;
            }

            .filter-panel {
                height: 200px;
            }

            #pagination-controls {
                flex-direction: column;
                gap: 10px;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <div class="history-events-container">
        <!-- 顶部标题栏 -->
        <header class="history-header">
            <div class="history-title">
                <i class="fas fa-calendar-alt"></i>
                <h1>历史事件</h1>
            </div>
        </header>

        <!-- 主内容区域 -->
        <main class="history-main">
            <!-- 左侧筛选面板 -->
            <section class="filter-panel">
                <div class="filter-header">
                    <h3><i class="fas fa-filter"></i>筛选条件</h3>
                </div>
                <div class="filter-content">
                    <!-- 事件类型筛选 -->
                    <div class="filter-section">
                        <h4><i class="fas fa-tags"></i>事件类型</h4>
                        <div class="filter-buttons">
                            <button class="filter-btn active" data-type="all">
                                <i class="fas fa-list"></i>
                                <span>所有事件</span>
                            </button>
                            <button class="filter-btn" data-type="报警设备">
                                <i class="fas fa-exclamation-triangle"></i>
                                <span>报警事件</span>
                            </button>
                            <button class="filter-btn" data-type="故障设备">
                                <i class="fas fa-times-circle"></i>
                                <span>故障事件</span>
                            </button>
                        </div>
                    </div>

                    <!-- 时间范围筛选 -->
                    <div class="filter-section">
                        <h4><i class="fas fa-clock"></i>时间范围</h4>
                        <div class="date-range-inputs">
                            <div class="quick-date-buttons">
                                <button class="filter-btn" onclick="setQuickDate('today')">
                                    <i class="fas fa-sun"></i>
                                    <span>今天</span>
                                </button>
                                <button class="filter-btn" onclick="setQuickDate('yesterday')">
                                    <i class="fas fa-moon"></i>
                                    <span>昨天</span>
                                </button>
                                <button class="filter-btn" onclick="setQuickDate('week')">
                                    <i class="fas fa-calendar-week"></i>
                                    <span>本周</span>
                                </button>
                                <button class="filter-btn" onclick="setQuickDate('month')">
                                    <i class="fas fa-calendar-alt"></i>
                                    <span>本月</span>
                                </button>
                            </div>
                            <div class="date-input-group">
                                <input type="datetime-local" class="date-input" id="startDate" placeholder="选择开始时间">
                                <i class="fas fa-calendar-alt date-input-icon"></i>
                            </div>
                            <div class="date-input-group">
                                <input type="datetime-local" class="date-input" id="endDate" placeholder="选择结束时间">
                                <i class="fas fa-calendar-alt date-input-icon"></i>
                            </div>
                            <button class="filter-btn" onclick="applyDateFilter()">
                                <i class="fas fa-search"></i>
                                <span>应用筛选</span>
                            </button>
                            <button class="filter-btn" onclick="resetDateFilter()" style="background: rgba(0, 212, 255, 0.08); border-color: rgba(0, 212, 255, 0.3); color: var(--text-secondary);">
                                <i class="fas fa-undo"></i>
                                <span>重置</span>
                            </button>
                        </div>
                    </div>


                </div>
            </section>

            <!-- 右侧事件列表 -->
            <section class="events-panel">
                <div class="events-header">
                    <h3><i class="fas fa-history"></i>历史事件记录</h3>
                    <div class="events-stats">
                        <div class="stat-item">
                            <i class="fas fa-list"></i>
                            <span>总计: <span class="stat-count" id="totalCount">0</span></span>
                        </div>
                        <div class="stat-item">
                            <i class="fas fa-exclamation-triangle" style="color: var(--warning-color);"></i>
                            <span>报警: <span class="stat-count" id="alarmCount">0</span></span>
                        </div>
                        <div class="stat-item">
                            <i class="fas fa-times-circle" style="color: var(--error-color);"></i>
                            <span>故障: <span class="stat-count" id="faultCount">0</span></span>
                        </div>
                        <div class="stat-item" id="mockModeIndicator" style="display: none;">
                            <i class="fas fa-database" style="color: var(--warning-color);"></i>
                            <span style="color: var(--warning-color);">模拟数据模式</span>
                        </div>
                    </div>
                </div>

                <!-- 表头 -->
                <div class="events-table-header">
                    <div class="header-cell serial">
                        <i class="fas fa-hashtag"></i>
                        <span>序号</span>
                    </div>
                    <div class="header-cell">
                        <i class="fas fa-clock"></i>
                        <span>时间</span>
                    </div>
                    <div class="header-cell">
                        <i class="fas fa-info-circle"></i>
                        <span>报警事件</span>
                    </div>
                </div>

                <!-- 事件列表内容 -->
                <div class="events-content" id="eventsContent">
                    <!-- 事件项将通过JavaScript动态生成 -->
                </div>
            </section>
        </main>
    </div>

    <script>
        /**
         * 历史事件页面脚本
         * 处理历史事件数据展示、筛选和交互功能
         */

        // 全局变量
        let allEvents = []; // 存储所有历史事件
        let filteredEvents = []; // 存储筛选后的事件
        let currentFilter = {
            type: 'all',
            startDate: null,
            endDate: null
        };
        let currentPage = 1; // 当前页码
        let pageSize = 50; // 每页数量
        let totalCount = 0; // 总记录数

        // API配置
        const API_CONFIG = {
            baseUrl: 'http://*************/prod-api',
            endpoints: {
                alertLog: '/iot/alertLog/list'
            },
            headers: {
                  'Accept': 'application/json',
                  ...getAuthHeader()
              }
        };

        // 开发模式配置（用于CORS问题时的本地测试）
        const DEV_CONFIG = {
            useMockData: false, // 设置为true使用模拟数据
            mockDelay: 1000 // 模拟网络延迟
        };

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('历史事件页面初始化开始...');
            initHistoryPage();
            console.log('历史事件页面初始化完成');
        });

        /**
         * 初始化历史事件页面
         */
        function initHistoryPage() {
            initEventFilters();
            validateInitialData();
            loadHistoryEvents();
        }



        /**
         * 初始化事件筛选器
         */
        function initEventFilters() {
            // 事件类型筛选按钮
            const typeButtons = document.querySelectorAll('[data-type]');
            typeButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // 移除其他按钮的active状态
                    typeButtons.forEach(btn => btn.classList.remove('active'));
                    // 添加当前按钮的active状态
                    this.classList.add('active');
                    // 更新筛选条件
                    currentFilter.type = this.dataset.type;
                    // 重新加载数据
                    currentPage = 1;
                    loadHistoryEvents();
                });
            });



            // 设置默认日期范围（最近7天）
            const now = new Date();
            const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

            document.getElementById('startDate').value = formatDateTimeLocal(sevenDaysAgo);
            document.getElementById('endDate').value = formatDateTimeLocal(now);

            // 设置默认的时间筛选
            currentFilter.startDate = sevenDaysAgo.getTime();
            currentFilter.endDate = now.getTime();

            // 为date-input-group添加点击事件，触发datetime-local输入框
            const dateInputGroups = document.querySelectorAll('.date-input-group');
            dateInputGroups.forEach(group => {
                // 将date-input-group设置为相对定位的容器
                group.style.position = 'relative';
                
                // 创建一个透明的覆盖层，确保点击能够传递到输入框
                const overlay = document.createElement('div');
                overlay.style.cssText = `
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    z-index: 2;
                    cursor: pointer;
                `;
                group.appendChild(overlay);
                
                // 为覆盖层添加点击事件
                overlay.addEventListener('click', function(e) {
                    e.stopPropagation();
                    const input = group.querySelector('input[type="datetime-local"]');
                    if (input && !input.disabled) {
                        // 直接触发输入框的点击
                        input.focus();
                        setTimeout(() => {
                            if (typeof input.showPicker === 'function') {
                                input.showPicker();
                            } else {
                                // 对于不支持showPicker的浏览器，模拟点击
                                input.click();
                            }
                        }, 0);
                    }
                });
            });
        }

        /**
         * 格式化日期时间为本地输入格式
         * @param {Date} date - 日期对象
         * @returns {string} 格式化的日期时间字符串
         */
        function formatDateTimeLocal(date) {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            const hours = String(date.getHours()).padStart(2, '0');
            const minutes = String(date.getMinutes()).padStart(2, '0');
            return `${year}-${month}-${day}T${hours}:${minutes}`;
        }

        /**
         * 从API加载历史事件数据
         * 调用接口：http://*************/prod-api/iot/alertLog/list
         * 请求方式：GET
         */
        async function loadHistoryEvents() {
            try {
                showLoading(true);

                // 如果启用了模拟数据模式，使用本地测试数据
                if (DEV_CONFIG.useMockData) {
                    console.log('使用模拟数据模式');
                    await new Promise(resolve => setTimeout(resolve, DEV_CONFIG.mockDelay));
                    const mockData = generateMockData();
                    processAPIData(mockData);
                    return;
                }

                // 构建请求参数
                const params = {
                    pageNum: currentPage,
                    pageSize: pageSize
                };

                // 添加事件类型筛选
                if (currentFilter.type !== 'all') {
                    params.alertName = currentFilter.type;
                }

                // 添加时间范围筛选
                if (currentFilter.startDate || currentFilter.endDate) {
                    params.params = {};
                    if (currentFilter.startDate) {
                        params.params.beginTime = formatDateTimeForAPI(new Date(currentFilter.startDate));
                    }
                    if (currentFilter.endDate) {
                        params.params.endTime = formatDateTimeForAPI(new Date(currentFilter.endDate));
                    }
                }

                console.log('请求参数:', params);

                // 构建GET请求的URL参数
                const urlParams = new URLSearchParams();
                urlParams.append('pageNum', params.pageNum);
                urlParams.append('pageSize', params.pageSize);

                // 添加事件类型筛选参数
                if (params.alertName) {
                    urlParams.append('alertName', params.alertName);
                }

                // 添加时间范围参数 - 使用params[beginTime]和params[endTime]格式
                if (params.params) {
                    if (params.params.beginTime) {
                        urlParams.append('params[beginTime]', params.params.beginTime);
                    }
                    if (params.params.endTime) {
                        urlParams.append('params[endTime]', params.params.endTime);
                    }
                }

                // 发送GET请求
                const apiUrl = API_CONFIG.baseUrl + API_CONFIG.endpoints.alertLog + '?' + urlParams.toString();
                console.log('GET请求URL:', apiUrl);

                const response = await fetch(apiUrl, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Authorization': API_CONFIG.headers.Authorization
                    },
                    mode: 'cors', // 明确指定CORS模式
                    credentials: 'omit' // 不发送cookies，避免CORS复杂化
                });

                if (!response.ok) {
                    let errorMessage = `HTTP ${response.status}`;

                    // 特殊处理认证错误
                    if (response.status === 401) {
                        errorMessage = '认证失败，请检查Authorization token是否有效';
                    } else if (response.status === 403) {
                        errorMessage = '权限不足，无法访问该接口';
                    } else {
                        try {
                            const errorData = await response.json();
                            errorMessage = errorData.msg || errorMessage;
                        } catch (e) {
                            // 忽略JSON解析错误，使用默认错误消息
                        }
                    }
                    throw new Error(errorMessage);
                }

                const data = await response.json();
                console.log('API响应数据:', data);

                if (data.code === 200) {
                    // 处理返回的数据
                    processAPIData(data);
                } else {
                    throw new Error(data.msg || '获取数据失败');
                }

            } catch (error) {
                console.error('加载历史事件数据失败:', error);

                // 特殊处理CORS错误
                let errorMessage = error.message;
                if (error.message.includes('CORS') || error.message.includes('fetch')) {
                    errorMessage = 'CORS跨域请求被阻止，请联系管理员配置服务器允许跨域访问';
                    console.error('CORS错误详情:', {
                        error: error,
                        apiUrl: API_CONFIG.baseUrl + API_CONFIG.endpoints.alertLog,
                        origin: window.location.origin,
                        solution: '需要在服务器端配置CORS允许当前域名访问'
                    });
                }

                showError('加载数据失败: ' + errorMessage);
            } finally {
                showLoading(false);
            }
        }

        /**
         * 处理API返回的数据
         * @param {Object} data - API返回的数据
         */
        function processAPIData(data) {
            totalCount = data.total || 0;
            allEvents = [];

            console.log('处理API数据:', {
                total: totalCount,
                rowsCount: data.rows ? data.rows.length : 0,
                currentPage: currentPage,
                pageSize: pageSize
            });

            if (data.rows && data.rows.length > 0) {
                console.log('开始处理数据行，总数:', data.rows.length);
                data.rows.forEach((item, index) => {
                    try {
                        console.log(`处理第${index + 1}条数据:`, {
                            alertLogId: item.alertLogId,
                            alertName: item.alertName,
                            createTime: item.createTime
                        });

                        // 只处理报警设备和故障设备，过滤掉恢复事件
                        if (item.alertName !== '报警设备' && item.alertName !== '故障设备') {
                            console.log(`跳过非目标事件类型: ${item.alertName}`);
                            return;
                        }

                        // 解析detail字段中的JSON数据，获取事件名称
                        let eventName = '';
                        if (item.detail) {
                            try {
                                const detailObj = JSON.parse(item.detail);
                                eventName = detailObj.name || '';
                            } catch (e) {
                                console.warn('解析detail字段失败:', e);
                                eventName = item.detail;
                            }
                        }

                        // 创建事件对象
                        const event = {
                            id: item.alertLogId,
                            alertName: item.alertName,
                            eventName: eventName,
                            createTime: item.createTime,
                            serialNumber: item.serialNumber,
                            deviceName: item.deviceName,
                            alertLevel: item.alertLevel,
                            status: item.status
                        };

                        allEvents.push(event);
                        console.log(`成功处理事件 ${event.id}: ${event.eventName}`);
                    } catch (error) {
                        console.error('处理事件数据失败:', error, item);
                    }
                });
            }

            console.log('数据处理完成:', {
                原始数据行数: data.rows ? data.rows.length : 0,
                处理后事件数: allEvents.length,
                事件列表: allEvents.map(e => ({ id: e.id, name: e.eventName, time: e.createTime }))
            });

            // 应用筛选并渲染
            applyFilters();

            // 添加分页控件
            addPaginationControls();
        }

        /**
         * 格式化日期时间为API所需格式
         * @param {Date} date - 日期对象
         * @returns {string} 格式化的日期时间字符串
         */
        function formatDateTimeForAPI(date) {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            const hours = String(date.getHours()).padStart(2, '0');
            const minutes = String(date.getMinutes()).padStart(2, '0');
            const seconds = String(date.getSeconds()).padStart(2, '0');
            return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
        }



        /**
         * 应用筛选条件
         */
        function applyFilters() {
            // 由于在processAPIData中已经过滤了恢复事件，这里直接使用所有数据
            filteredEvents = [...allEvents];

            console.log('应用筛选条件:', {
                allEventsCount: allEvents.length,
                filteredEventsCount: filteredEvents.length,
                currentFilter: currentFilter
            });

            renderEventsList();
            updateStatistics();
        }

        /**
         * 应用日期筛选
         */
        function applyDateFilter() {
            const startDateInput = document.getElementById('startDate');
            const endDateInput = document.getElementById('endDate');

            // 验证日期格式
            if (startDateInput.value && !isValidDate(startDateInput.value)) {
                alert('开始时间格式不正确，请选择有效的时间');
                return;
            }

            if (endDateInput.value && !isValidDate(endDateInput.value)) {
                alert('结束时间格式不正确，请选择有效的时间');
                return;
            }

            // 验证时间范围逻辑
            if (startDateInput.value && endDateInput.value) {
                const startTime = new Date(startDateInput.value).getTime();
                const endTime = new Date(endDateInput.value).getTime();
                
                if (startTime > endTime) {
                    alert('开始时间不能晚于结束时间');
                    return;
                }

                // 限制最大查询范围为90天
                const maxRange = 90 * 24 * 60 * 60 * 1000; // 90天
                if (endTime - startTime > maxRange) {
                    alert('查询时间范围不能超过90天，请缩小时间范围');
                    return;
                }
            }

            if (startDateInput.value) {
                currentFilter.startDate = new Date(startDateInput.value).getTime();
            } else {
                currentFilter.startDate = null;
            }

            if (endDateInput.value) {
                currentFilter.endDate = new Date(endDateInput.value).getTime();
            } else {
                currentFilter.endDate = null;
            }

            console.log('应用时间筛选:', {
                startDate: startDateInput.value,
                endDate: endDateInput.value,
                startTime: currentFilter.startDate,
                endTime: currentFilter.endDate
            });

            // 重新加载数据（因为时间筛选需要在服务端完成）
            currentPage = 1;
            loadHistoryEvents();
        }

        /**
         * 验证日期字符串是否有效
         * @param {string} dateStr - 日期字符串
         * @returns {boolean} - 是否有效
         */
        function isValidDate(dateStr) {
            const date = new Date(dateStr);
            return date instanceof Date && !isNaN(date.getTime());
        }

        /**
         * 渲染事件列表
         */
        function renderEventsList() {
            const eventsContent = document.getElementById('eventsContent');

            console.log('渲染事件列表:', {
                filteredEventsCount: filteredEvents.length,
                currentPage: currentPage,
                totalCount: totalCount,
                eventsContentExists: !!eventsContent,
                eventsContentId: eventsContent ? eventsContent.id : 'null'
            });

            if (!eventsContent) {
                console.error('找不到eventsContent元素！');
                return;
            }

            if (filteredEvents.length === 0) {
                eventsContent.innerHTML = `
                    <div class="empty-state">
                        <i class="fas fa-inbox"></i>
                        <p>暂无符合条件的历史事件</p>
                    </div>
                `;
                return;
            }

            const eventsHtml = filteredEvents.map((event, index) => {
                // 确定事件类型的CSS类名
                const eventTypeClass = event.alertName === '报警设备' ? 'alarm' : 'fault';

                // 格式化时间显示（确保格式为 YYYY-MM-DD HH:mm:ss）
                const formattedTime = formatDisplayTime(event.createTime);

                console.log(`渲染事件 ${index + 1}:`, {
                    id: event.id,
                    name: event.eventName,
                    time: formattedTime,
                    type: event.alertName
                });

                return `
                    <div class="event-item ${eventTypeClass}" data-event-id="${event.id}">
                        <span class="event-serial">${index + 1}</span>
                        <span class="event-datetime">${formattedTime}</span>
                        <span class="event-message" title="${event.eventName}">${event.eventName}</span>
                    </div>
                `;
            }).join('');

            console.log('生成的HTML长度:', eventsHtml.length);
            console.log('HTML内容预览:', eventsHtml.substring(0, 200) + '...');

            eventsContent.innerHTML = eventsHtml;

            console.log('DOM更新完成，当前eventsContent子元素数量:', eventsContent.children.length);

            // 添加点击事件监听
            const eventItems = eventsContent.querySelectorAll('.event-item');
            eventItems.forEach(item => {
                item.addEventListener('click', function() {
                    const eventId = parseInt(this.dataset.eventId);
                    showEventDetails(eventId);
                });
            });
        }

        /**
         * 格式化时间显示
         * @param {string} timeString - 时间字符串
         * @returns {string} 格式化后的时间字符串
         */
        function formatDisplayTime(timeString) {
            if (!timeString) return '';

            // 如果已经是正确格式，直接返回
            if (/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/.test(timeString)) {
                return timeString;
            }

            // 尝试解析并格式化
            try {
                const date = new Date(timeString);
                if (isNaN(date.getTime())) {
                    return timeString; // 如果解析失败，返回原字符串
                }

                const year = date.getFullYear();
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const day = String(date.getDate()).padStart(2, '0');
                const hours = String(date.getHours()).padStart(2, '0');
                const minutes = String(date.getMinutes()).padStart(2, '0');
                const seconds = String(date.getSeconds()).padStart(2, '0');

                return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
            } catch (error) {
                console.warn('时间格式化失败:', error, timeString);
                return timeString;
            }
        }

        /**
         * 更新统计信息
         */
        function updateStatistics() {
            const displayTotal = filteredEvents.length;
            const alarmCount = filteredEvents.filter(event => event.alertName === '报警设备').length;
            const faultCount = filteredEvents.filter(event => event.alertName === '故障设备').length;

            document.getElementById('totalCount').textContent = displayTotal;
            document.getElementById('alarmCount').textContent = alarmCount;
            document.getElementById('faultCount').textContent = faultCount;
        }

        /**
         * 显示事件详情（可扩展功能）
         * @param {number} eventId - 事件ID
         */
        function showEventDetails(eventId) {
            const event = allEvents.find(e => e.id === eventId);
            if (event) {
                console.log('显示事件详情:', event);
                // 格式化详情信息
                const details = [
                    `事件ID: ${event.id}`,
                    `事件类型: ${event.alertName}`,
                    `事件名称: ${event.eventName}`,
                    `发生时间: ${event.createTime}`,
                    `设备序列号: ${event.serialNumber}`,
                    `告警级别: ${event.alertLevel}`,
                    `状态: ${event.status}`
                ].join('\n');

                alert(`事件详情:\n\n${details}`);
            }
        }

        /**
         * 初始化页面时的数据验证
         */
        function validateInitialData() {
            console.log('开始数据验证...');
            console.log('当前筛选条件:', currentFilter);
            console.log('当前页码:', currentPage);
            console.log('每页数量:', pageSize);

            // 验证认证token
            validateAuthToken();

            // 验证时间范围设置
            if (currentFilter.startDate && currentFilter.endDate) {
                const timeDiff = currentFilter.endDate - currentFilter.startDate;
                const daysDiff = timeDiff / (1000 * 60 * 60 * 24);
                console.log(`时间范围: ${daysDiff.toFixed(1)} 天`);

                if (daysDiff > 30) {
                    console.warn('时间范围超过30天，可能影响性能');
                }
            }
        }

        /**
         * 验证认证token的有效性
         */
        function validateAuthToken() {
            const token = API_CONFIG.headers.Authorization;
            if (!token || !token.startsWith('Bearer ')) {
                console.error('认证token格式错误');
                return false;
            }

            try {
                // 解析JWT token的payload部分（仅用于基本验证，不验证签名）
                const tokenParts = token.replace('Bearer ', '').split('.');
                if (tokenParts.length !== 3) {
                    console.error('JWT token格式无效');
                    return false;
                }

                const payload = JSON.parse(atob(tokenParts[1]));
                console.log('Token payload:', payload);

                // 检查token是否过期（如果有exp字段）
                if (payload.exp) {
                    const currentTime = Math.floor(Date.now() / 1000);
                    if (payload.exp < currentTime) {
                        console.warn('认证token已过期');
                        return false;
                    }
                }

                console.log('认证token验证通过');
                return true;
            } catch (error) {
                console.error('解析认证token失败:', error);
                return false;
            }
        }

        /**
         * 显示加载状态
         * @param {boolean} show - 是否显示加载状态
         */
        function showLoading(show) {
            const eventsContent = document.getElementById('eventsContent');
            if (show) {
                eventsContent.innerHTML = `
                    <div class="empty-state">
                        <i class="fas fa-spinner fa-spin"></i>
                        <p>正在加载历史事件数据...</p>
                        <p style="font-size: 11px; color: var(--text-muted); margin-top: 10px;">
                            页码: ${currentPage} | 每页: ${pageSize} 条
                        </p>
                    </div>
                `;

                // 隐藏分页控件
                const paginationDiv = document.getElementById('pagination-controls');
                if (paginationDiv) {
                    paginationDiv.style.display = 'none';
                }
            } else {
                // 显示分页控件
                const paginationDiv = document.getElementById('pagination-controls');
                if (paginationDiv) {
                    paginationDiv.style.display = 'flex';
                }
            }
        }

        /**
         * 显示错误信息
         * @param {string} message - 错误信息
         */
        function showError(message) {
            const eventsContent = document.getElementById('eventsContent');

            // 检查是否是CORS错误，提供更详细的解决方案
            const isCorsError = message.includes('CORS') || message.includes('跨域');

            let errorContent = `
                <div class="empty-state">
                    <i class="fas fa-exclamation-triangle" style="color: var(--error-color);"></i>
                    <p>${message}</p>
            `;

            if (isCorsError) {
                errorContent += `
                    <div style="margin-top: 15px; padding: 15px; background: rgba(255, 193, 7, 0.1); border: 1px solid rgba(255, 193, 7, 0.3); border-radius: 8px; text-align: left; max-width: 500px;">
                        <h4 style="color: var(--warning-color); margin: 0 0 10px 0; font-size: 14px;">
                            <i class="fas fa-info-circle"></i> CORS解决方案
                        </h4>
                        <ul style="font-size: 12px; color: var(--text-secondary); margin: 0; padding-left: 20px;">
                            <li>联系后端开发者在服务器配置CORS允许当前域名</li>
                            <li>或者将页面部署到与API相同的域名下</li>
                            <li>或者使用代理服务器转发请求</li>
                            <li>当前页面域名: <code>${window.location.origin}</code></li>
                            <li>API服务器: <code>${API_CONFIG.baseUrl}</code></li>
                        </ul>
                    </div>
                `;
            }

            errorContent += `
                    <button class="filter-btn" onclick="loadHistoryEvents()" style="margin-top: 15px;">
                        <i class="fas fa-redo"></i>
                        <span>重新加载</span>
                    </button>
                    ${isCorsError ? `
                    <button class="filter-btn" onclick="checkCorsStatus()" style="margin-top: 10px; margin-left: 10px;">
                        <i class="fas fa-network-wired"></i>
                        <span>检测CORS状态</span>
                    </button>
                    <button class="filter-btn" onclick="enableMockDataMode()" style="margin-top: 10px; margin-left: 10px; background: rgba(255, 193, 7, 0.2); border-color: var(--warning-color);">
                        <i class="fas fa-database"></i>
                        <span>使用模拟数据</span>
                    </button>
                    ` : ''}
                </div>
            `;

            eventsContent.innerHTML = errorContent;
        }

        /**
         * 添加分页控制功能
         */
        function addPaginationControls() {
            const eventsPanel = document.querySelector('.events-panel');

            // 检查是否已存在分页控件
            let paginationDiv = document.getElementById('pagination-controls');
            if (!paginationDiv) {
                paginationDiv = document.createElement('div');
                paginationDiv.id = 'pagination-controls';
                paginationDiv.style.cssText = `
                    padding: 15px 20px;
                    border-top: 1px solid var(--border-color);
                    background: rgba(0, 212, 255, 0.05);
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    font-size: 12px;
                `;
                eventsPanel.appendChild(paginationDiv);
            }

            const totalPages = Math.ceil(totalCount / pageSize);
            const startRecord = (currentPage - 1) * pageSize + 1;
            const endRecord = Math.min(currentPage * pageSize, totalCount);

            paginationDiv.innerHTML = `
                <div style="color: var(--text-secondary);">
                    显示 ${startRecord}-${endRecord} 条，共 ${totalCount} 条记录
                </div>
                <div style="display: flex; gap: 10px; align-items: center;">
                    <button class="filter-btn" onclick="changePage(${currentPage - 1})"
                            ${currentPage <= 1 ? 'disabled style="opacity: 0.5; cursor: not-allowed;"' : ''}>
                        <i class="fas fa-chevron-left"></i>
                        <span>上一页</span>
                    </button>
                    <span style="color: var(--primary-color); font-weight: bold;">
                        第 ${currentPage} / ${totalPages} 页
                    </span>
                    <button class="filter-btn" onclick="changePage(${currentPage + 1})"
                            ${currentPage >= totalPages ? 'disabled style="opacity: 0.5; cursor: not-allowed;"' : ''}>
                        <span>下一页</span>
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            `;
        }

        /**
         * 切换页码
         * @param {number} page - 目标页码
         */
        function changePage(page) {
            const totalPages = Math.ceil(totalCount / pageSize);
            console.log('切换页码:', {
                requestedPage: page,
                currentPage: currentPage,
                totalPages: totalPages,
                totalCount: totalCount,
                pageSize: pageSize
            });

            if (page < 1 || page > totalPages) {
                console.warn('页码超出范围:', page, '有效范围: 1 -', totalPages);
                return;
            }

            currentPage = page;
            console.log('开始加载第', currentPage, '页数据');
            loadHistoryEvents();
        }

        /**
         * 检测CORS状态
         */
        async function checkCorsStatus() {
            console.log('开始检测CORS状态...');

            const eventsContent = document.getElementById('eventsContent');
            eventsContent.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-spinner fa-spin"></i>
                    <p>正在检测CORS状态...</p>
                </div>
            `;

            try {
                // 尝试简单的GET请求检测CORS（带基本参数）
                const testUrl = API_CONFIG.baseUrl + API_CONFIG.endpoints.alertLog + '?pageNum=1&pageSize=1';

                const response = await fetch(testUrl, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Authorization': API_CONFIG.headers.Authorization
                    },
                    mode: 'cors'
                });

                console.log('CORS检测结果:', {
                    status: response.status,
                    headers: [...response.headers.entries()]
                });

                eventsContent.innerHTML = `
                    <div class="empty-state">
                        <i class="fas fa-check-circle" style="color: var(--success-color);"></i>
                        <p>CORS预检请求成功</p>
                        <div style="margin-top: 15px; padding: 15px; background: rgba(40, 167, 69, 0.1); border: 1px solid rgba(40, 167, 69, 0.3); border-radius: 8px; text-align: left; max-width: 500px;">
                            <h4 style="color: var(--success-color); margin: 0 0 10px 0; font-size: 14px;">
                                <i class="fas fa-info-circle"></i> 检测结果
                            </h4>
                            <ul style="font-size: 12px; color: var(--text-secondary); margin: 0; padding-left: 20px;">
                                <li>服务器响应状态: ${response.status}</li>
                                <li>GET请求测试成功</li>
                                <li>如果仍有问题，可能是认证或参数格式问题</li>
                            </ul>
                        </div>
                        <button class="filter-btn" onclick="loadHistoryEvents()" style="margin-top: 15px;">
                            <i class="fas fa-redo"></i>
                            <span>重新尝试加载数据</span>
                        </button>
                    </div>
                `;

            } catch (error) {
                console.error('CORS检测失败:', error);

                eventsContent.innerHTML = `
                    <div class="empty-state">
                        <i class="fas fa-times-circle" style="color: var(--error-color);"></i>
                        <p>CORS检测失败</p>
                        <div style="margin-top: 15px; padding: 15px; background: rgba(220, 53, 69, 0.1); border: 1px solid rgba(220, 53, 69, 0.3); border-radius: 8px; text-align: left; max-width: 500px;">
                            <h4 style="color: var(--error-color); margin: 0 0 10px 0; font-size: 14px;">
                                <i class="fas fa-exclamation-triangle"></i> 检测结果
                            </h4>
                            <ul style="font-size: 12px; color: var(--text-secondary); margin: 0; padding-left: 20px;">
                                <li>服务器不允许跨域访问</li>
                                <li>错误信息: ${error.message}</li>
                                <li>需要后端配置CORS允许当前域名访问</li>
                                <li>或使用代理服务器转发请求</li>
                            </ul>
                        </div>
                        <button class="filter-btn" onclick="loadHistoryEvents()" style="margin-top: 15px;">
                            <i class="fas fa-redo"></i>
                            <span>重新尝试加载数据</span>
                        </button>
                    </div>
                `;
            }
        }

        /**
         * 生成模拟数据（用于CORS问题时的本地测试）
         */
        function generateMockData() {
            const mockEvents = [];
            const eventTypes = ['报警设备', '故障设备'];
            const eventNames = [
                'A相PWM板ROM参数故障',
                'B相PWM板ROM参数故障',
                'C相PWM板ROM参数故障',
                '主控板通信故障',
                '温度传感器异常',
                '电压超限报警',
                '电流过载保护',
                '绝缘监测报警'
            ];

            // 生成当前页的模拟数据
            const startIndex = (currentPage - 1) * pageSize;
            for (let i = 0; i < pageSize; i++) {
                const now = new Date();
                const randomHours = Math.floor(Math.random() * 24 * 7); // 最近7天内
                const eventTime = new Date(now.getTime() - randomHours * 60 * 60 * 1000);

                const eventType = eventTypes[Math.floor(Math.random() * eventTypes.length)];
                const eventName = eventNames[Math.floor(Math.random() * eventNames.length)];

                mockEvents.push({
                    alertLogId: 135900 + startIndex + i, // 根据页码生成不同的ID
                    alertName: eventType,
                    alertLevel: Math.floor(Math.random() * 3) + 1,
                    status: 2,
                    serialNumber: `D19DWJ1674O${String((startIndex + i) % 100).padStart(2, '0')}`,
                    productId: 190,
                    detail: JSON.stringify({
                        id: `HMI_3003${startIndex + i}_3`,
                        name: eventName,
                        value: "1"
                    }),
                    createBy: "bydq_admin",
                    createTime: formatDateTimeForAPI(eventTime),
                    updateBy: "",
                    updateTime: null,
                    remark: null,
                    userId: 19,
                    deviceName: eventType,
                    deviceId: null,
                    sceneId: 123 + ((startIndex + i) % 3),
                    sceneName: null,
                    beginTime: null,
                    endTime: null,
                    deptUserId: null
                });
            }

            return {
                code: 200,
                msg: "查询成功（模拟数据）",
                total: 1000 + Math.floor(Math.random() * 500), // 模拟总数
                rows: mockEvents
            };
        }

        /**
         * 切换到模拟数据模式（用于CORS问题调试）
         */
        function enableMockDataMode() {
            DEV_CONFIG.useMockData = true;
            console.log('已启用模拟数据模式');

            // 显示模拟数据模式指示器
            const indicator = document.getElementById('mockModeIndicator');
            if (indicator) {
                indicator.style.display = 'flex';
            }

            currentPage = 1;
            loadHistoryEvents();
        }

        /**
         * 切换到真实API模式
         */
        function disableMockDataMode() {
            DEV_CONFIG.useMockData = false;
            console.log('已切换到真实API模式');

            // 隐藏模拟数据模式指示器
            const indicator = document.getElementById('mockModeIndicator');
            if (indicator) {
                indicator.style.display = 'none';
            }

            currentPage = 1;
            loadHistoryEvents();
        }

        /**
         * 调试函数：检查当前状态
         */
        function debugCurrentState() {
            console.log('=== 当前状态调试信息 ===');
            console.log('当前页码:', currentPage);
            console.log('总记录数:', totalCount);
            console.log('每页数量:', pageSize);
            console.log('总页数:', Math.ceil(totalCount / pageSize));
            console.log('所有事件数量:', allEvents.length);
            console.log('筛选后事件数量:', filteredEvents.length);
            console.log('模拟数据模式:', DEV_CONFIG.useMockData);
            console.log('当前筛选条件:', currentFilter);

            const eventsContent = document.getElementById('eventsContent');
            console.log('eventsContent元素存在:', !!eventsContent);
            if (eventsContent) {
                console.log('eventsContent子元素数量:', eventsContent.children.length);
                console.log('eventsContent内容长度:', eventsContent.innerHTML.length);
            }

            console.log('最近5个事件:', filteredEvents.slice(0, 5).map(e => ({
                id: e.id,
                name: e.eventName,
                time: e.createTime,
                type: e.alertName
            })));
            console.log('=== 调试信息结束 ===');
        }

        /**
         * 设置快速时间范围
         * @param {string} type - 时间类型: today, yesterday, week, month
         */
        function setQuickDate(type) {
            const now = new Date();
            let startDate, endDate;

            switch(type) {
                case 'today':
                    startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 0, 0, 0);
                    endDate = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59);
                    break;
                case 'yesterday':
                    startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 1, 0, 0, 0);
                    endDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 1, 23, 59, 59);
                    break;
                case 'week':
                    const weekStart = new Date(now);
                    weekStart.setDate(now.getDate() - now.getDay());
                    startDate = new Date(weekStart.getFullYear(), weekStart.getMonth(), weekStart.getDate(), 0, 0, 0);
                    endDate = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59);
                    break;
                case 'month':
                    startDate = new Date(now.getFullYear(), now.getMonth(), 1, 0, 0, 0);
                    endDate = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59);
                    break;
            }

            document.getElementById('startDate').value = formatDateTimeLocal(startDate);
            document.getElementById('endDate').value = formatDateTimeLocal(endDate);
            
            // 自动应用筛选
            applyDateFilter();
        }

        /**
         * 重置时间筛选
         */
        function resetDateFilter() {
            // 重置为默认的最近7天时间范围
            const now = new Date();
            const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
            
            document.getElementById('startDate').value = formatDateTimeLocal(sevenDaysAgo);
            document.getElementById('endDate').value = formatDateTimeLocal(now);
            
            currentFilter.startDate = sevenDaysAgo.getTime();
            currentFilter.endDate = now.getTime();
            
            // 重新加载数据
            currentPage = 1;
            loadHistoryEvents();
        }
    </script>
</body>
</html>
